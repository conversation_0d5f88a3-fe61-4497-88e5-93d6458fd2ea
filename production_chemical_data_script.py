#!/usr/bin/env python3
"""
Production Chemical Transaction Data Processor
==============================================

This script processes chemical transaction data from multiple sources and creates
a consolidated CSV file for production use.

Input files:
- data.txt: Transaction data for different time periods (3, 6, 9, 12 months)
- Supplier Retrieval Data validation - Sheet8.csv: Chemical names and HS codes

Output:
- chemical_transaction_data.csv: Consolidated data with all chemicals and time periods

Author: AI Assistant
Version: 1.0 (Production Ready)
"""

import csv
import sys
import os
from collections import defaultdict
from typing import Dict, List, Tuple, Any

def normalize_hs_code(hs_code: str) -> str:
    """
    Remove dots and standardize HS code format.
    
    Args:
        hs_code: Raw HS code string
        
    Returns:
        Normalized HS code string
    """
    if isinstance(hs_code, str):
        return hs_code.replace('.', '').strip()
    return str(hs_code).replace('.', '').strip()

def validate_input_files() -> bool:
    """
    Validate that all required input files exist and are readable.
    
    Returns:
        True if all files are valid, False otherwise
    """
    required_files = [
        'data.txt',
        'Supplier Retrieval Data validation - Sheet8.csv'
    ]
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            print(f"❌ ERROR: Required file '{file_path}' not found!")
            return False
        
        if not os.access(file_path, os.R_OK):
            print(f"❌ ERROR: Cannot read file '{file_path}'!")
            return False
    
    print("✅ All input files validated successfully")
    return True

def parse_transaction_data() -> Dict[str, Dict[str, Dict[str, int]]]:
    """
    Parse the data.txt file to extract transaction data for all time periods.
    
    Returns:
        Dictionary with structure: {period: {hs_code: {metric: value}}}
    """
    print("📊 Parsing transaction data...")
    
    transaction_data = {
        '3_months': {},
        '6_months': {},
        '9_months': {},
        '12_months': {}
    }
    
    current_period = None
    current_record = {}
    
    def save_current_record():
        """Helper function to save the current record"""
        if current_record and current_period:
            hs_code = normalize_hs_code(current_record['hs_code'])
            transaction_data[current_period][hs_code] = {
                'countries': current_record.get('total_exporting_countries', 0),
                'exporters': current_record.get('total_unique_exporters', 0),
                'transactions': current_record.get('total_transactions', 0)
            }
    
    try:
        with open('data.txt', 'r', encoding='utf-8') as file:
            for line_num, line in enumerate(file, 1):
                line = line.strip()
                
                # Identify time period sections
                if 'for 3 months' in line:
                    save_current_record()
                    current_period = '3_months'
                    current_record = {}
                    continue
                elif 'for 6 months' in line:
                    save_current_record()
                    current_period = '6_months'
                    current_record = {}
                    continue
                elif 'for 9months' in line:
                    save_current_record()
                    current_period = '9_months'
                    current_record = {}
                    continue
                elif 'for 12 months' in line:
                    save_current_record()
                    current_period = '12_months'
                    current_record = {}
                    continue
                
                # Parse record data
                if current_period and '|' in line:
                    parts = line.split('|')
                    if len(parts) == 2:
                        key = parts[0].strip()
                        value = parts[1].strip()
                        
                        if key == 'hs_code':
                            save_current_record()
                            current_record = {'hs_code': value}
                        else:
                            try:
                                current_record[key] = int(value) if value.isdigit() else value
                            except ValueError:
                                print(f"⚠️  Warning: Invalid value '{value}' for key '{key}' at line {line_num}")
        
        # Save the last record
        save_current_record()
        
    except Exception as e:
        print(f"❌ ERROR: Failed to parse data.txt: {e}")
        sys.exit(1)
    
    # Print summary
    total_hs_codes = 0
    for period, data in transaction_data.items():
        count = len(data)
        total_hs_codes += count
        print(f"   {period}: {count} HS codes")
    
    unique_hs_codes = set()
    for period_data in transaction_data.values():
        unique_hs_codes.update(period_data.keys())
    
    print(f"   Total unique HS codes: {len(unique_hs_codes)}")
    
    return transaction_data

def parse_chemical_mapping() -> List[Tuple[str, str]]:
    """
    Parse the chemical names and HS codes from CSV file.
    
    Returns:
        List of tuples: [(chemical_name, hs_code), ...]
    """
    print("🧪 Parsing chemical mapping...")
    
    chemicals = []
    
    try:
        with open('Supplier Retrieval Data validation - Sheet8.csv', 'r', encoding='utf-8') as file:
            reader = csv.reader(file)
            next(reader)  # Skip header
            next(reader)  # Skip empty row
            
            for row_num, row in enumerate(reader, 3):
                if len(row) >= 2 and row[0].strip() and row[1].strip():
                    chemical_name = row[0].strip()
                    hs_code = normalize_hs_code(row[1].strip())
                    chemicals.append((chemical_name, hs_code))
                elif len(row) >= 1 and row[0].strip():
                    print(f"⚠️  Warning: Row {row_num} missing HS code for '{row[0].strip()}'")
    
    except Exception as e:
        print(f"❌ ERROR: Failed to parse chemical CSV: {e}")
        sys.exit(1)
    
    print(f"   Successfully parsed {len(chemicals)} chemicals")
    
    # Check for chemicals sharing HS codes
    hs_code_count = defaultdict(list)
    for chemical_name, hs_code in chemicals:
        hs_code_count[hs_code].append(chemical_name)
    
    shared_hs_codes = {hs: names for hs, names in hs_code_count.items() if len(names) > 1}
    if shared_hs_codes:
        print(f"   {len(shared_hs_codes)} HS codes have multiple chemicals (as expected)")
    
    return chemicals

def create_consolidated_csv(transaction_data: Dict, chemicals: List[Tuple[str, str]]) -> None:
    """
    Create the final consolidated CSV file.
    
    Args:
        transaction_data: Parsed transaction data
        chemicals: List of chemical name and HS code pairs
    """
    print("📝 Creating consolidated CSV...")
    
    headers = [
        'name_of_chemical',
        'hs_code',
        'countries_last_3_months',
        'exporters_last_3_months', 
        'transactions_last_3_months',
        'countries_last_6_months',
        'exporters_last_6_months',
        'transactions_last_6_months',
        'countries_last_9_months',
        'exporters_last_9_months',
        'transactions_last_9_months',
        'countries_last_12_months',
        'exporters_last_12_months',
        'transactions_last_12_months'
    ]
    
    output_file = 'chemical_transaction_data.csv'
    
    try:
        with open(output_file, 'w', newline='', encoding='utf-8') as file:
            writer = csv.writer(file)
            writer.writerow(headers)
            
            chemicals_with_data = 0
            chemicals_without_data = []
            
            for chemical_name, hs_code in chemicals:
                row = [chemical_name, hs_code]
                has_any_data = False
                
                # Add data for each time period
                for period in ['3_months', '6_months', '9_months', '12_months']:
                    if hs_code in transaction_data[period]:
                        data = transaction_data[period][hs_code]
                        row.extend([
                            data['countries'],
                            data['exporters'],
                            data['transactions']
                        ])
                        has_any_data = True
                    else:
                        # No data found for this HS code in this period
                        row.extend([0, 0, 0])
                
                writer.writerow(row)
                
                if has_any_data:
                    chemicals_with_data += 1
                else:
                    chemicals_without_data.append((chemical_name, hs_code))
        
        print(f"✅ Successfully created '{output_file}'")
        print(f"   Total chemicals: {len(chemicals)}")
        print(f"   Chemicals with data: {chemicals_with_data}")
        print(f"   Chemicals without data: {len(chemicals_without_data)}")
        
        if chemicals_without_data:
            print("   Chemicals without transaction data:")
            for name, hs in chemicals_without_data:
                print(f"     - {name} ({hs})")
    
    except Exception as e:
        print(f"❌ ERROR: Failed to create CSV file: {e}")
        sys.exit(1)

def main():
    """Main execution function"""
    print("🚀 CHEMICAL TRANSACTION DATA PROCESSOR - PRODUCTION VERSION")
    print("=" * 60)
    
    # Step 1: Validate input files
    if not validate_input_files():
        sys.exit(1)
    
    # Step 2: Parse transaction data
    transaction_data = parse_transaction_data()
    
    # Step 3: Parse chemical mapping
    chemicals = parse_chemical_mapping()
    
    # Step 4: Create consolidated CSV
    create_consolidated_csv(transaction_data, chemicals)
    
    print("\n🎉 PROCESSING COMPLETED SUCCESSFULLY!")
    print("   Output file: chemical_transaction_data.csv")
    print("   Ready for production use!")

if __name__ == "__main__":
    main()
