import csv
import re
from collections import defaultdict

def normalize_hs_code(hs_code):
    """Remove dots and standardize HS code format"""
    if isinstance(hs_code, str):
        return hs_code.replace('.', '').strip()
    return str(hs_code).replace('.', '').strip()

def comprehensive_test():
    """Run comprehensive tests to verify the script is working correctly"""
    
    print("=== COMPREHENSIVE PRODUCTION TEST ===\n")
    
    # Test 1: Check if files exist
    print("1. Checking if required files exist...")
    try:
        with open('data.txt', 'r') as f:
            data_lines = len(f.readlines())
        print(f"   ✅ data.txt exists ({data_lines} lines)")
    except FileNotFoundError:
        print("   ❌ data.txt NOT FOUND!")
        return False
    
    try:
        with open('Supplier Retrieval Data validation - Sheet8.csv', 'r') as f:
            csv_lines = len(f.readlines())
        print(f"   ✅ CSV file exists ({csv_lines} lines)")
    except FileNotFoundError:
        print("   ❌ CSV file NOT FOUND!")
        return False
    
    # Test 2: Parse and verify data.txt structure
    print("\n2. Testing data.txt parsing...")
    transaction_data = parse_data_txt_test()
    
    # Test 3: Parse and verify CSV structure  
    print("\n3. Testing CSV parsing...")
    chemicals = parse_chemical_csv_test()
    
    # Test 4: Check specific test cases
    print("\n4. Testing specific cases...")
    test_specific_cases(transaction_data, chemicals)
    
    # Test 5: Generate final CSV and verify
    print("\n5. Generating and verifying final CSV...")
    create_and_verify_final_csv(transaction_data, chemicals)
    
    print("\n=== ALL TESTS COMPLETED ===")
    return True

def parse_data_txt_test():
    """Parse data.txt with detailed logging"""
    transaction_data = {
        '3_months': {},
        '6_months': {},
        '9_months': {},
        '12_months': {}
    }
    
    current_period = None
    current_record = {}
    period_counts = {'3_months': 0, '6_months': 0, '9_months': 0, '12_months': 0}
    
    def save_current_record():
        if current_record and current_period:
            hs_code = normalize_hs_code(current_record['hs_code'])
            transaction_data[current_period][hs_code] = {
                'countries': current_record.get('total_exporting_countries', 0),
                'exporters': current_record.get('total_unique_exporters', 0),
                'transactions': current_record.get('total_transactions', 0)
            }
            period_counts[current_period] += 1
    
    with open('data.txt', 'r') as file:
        for line_num, line in enumerate(file, 1):
            line = line.strip()
            
            # Identify time period
            if 'for 3 months' in line:
                save_current_record()
                current_period = '3_months'
                current_record = {}
                continue
            elif 'for 6 months' in line:
                save_current_record()
                current_period = '6_months'
                current_record = {}
                continue
            elif 'for 9months' in line:
                save_current_record()
                current_period = '9_months'
                current_record = {}
                continue
            elif 'for 12 months' in line:
                save_current_record()
                current_period = '12_months'
                current_record = {}
                continue
            
            # Parse record data
            if current_period and '|' in line:
                parts = line.split('|')
                if len(parts) == 2:
                    key = parts[0].strip()
                    value = parts[1].strip()
                    
                    if key == 'hs_code':
                        save_current_record()
                        current_record = {'hs_code': value}
                    else:
                        current_record[key] = int(value) if value.isdigit() else value
    
    # Save the last record
    save_current_record()
    
    # Print results
    for period, count in period_counts.items():
        print(f"   {period}: {count} HS codes")
    
    total_unique_hs = set()
    for period_data in transaction_data.values():
        total_unique_hs.update(period_data.keys())
    print(f"   Total unique HS codes across all periods: {len(total_unique_hs)}")
    
    return transaction_data

def parse_chemical_csv_test():
    """Parse CSV with detailed logging"""
    chemicals = []
    
    with open('Supplier Retrieval Data validation - Sheet8.csv', 'r') as file:
        reader = csv.reader(file)
        next(reader)  # Skip header
        next(reader)  # Skip empty row
        
        for row_num, row in enumerate(reader, 3):  # Start from row 3
            if len(row) >= 2 and row[0].strip() and row[1].strip():
                chemical_name = row[0].strip()
                hs_code = normalize_hs_code(row[1].strip())
                chemicals.append((chemical_name, hs_code))
            elif len(row) >= 1 and row[0].strip():
                print(f"   ⚠️  Row {row_num}: Missing HS code for '{row[0].strip()}'")
    
    print(f"   Successfully parsed {len(chemicals)} chemicals")
    
    # Check for duplicates
    hs_code_count = defaultdict(list)
    for chemical_name, hs_code in chemicals:
        hs_code_count[hs_code].append(chemical_name)
    
    shared_count = sum(1 for names in hs_code_count.values() if len(names) > 1)
    print(f"   {shared_count} HS codes have multiple chemicals")
    
    return chemicals

def test_specific_cases(transaction_data, chemicals):
    """Test specific problematic cases"""

    # Test case 1: HS code 34021300 (was problematic before)
    print("   Testing HS code 34021300...")
    test_hs = '34021300'
    for period in ['3_months', '6_months', '9_months', '12_months']:
        if test_hs in transaction_data[period]:
            data = transaction_data[period][test_hs]
            print(f"     {period}: {data['countries']} countries, {data['exporters']} exporters, {data['transactions']} transactions")
        else:
            print(f"     {period}: NO DATA")

    # Test case 2: Check if all chemicals from CSV have some data
    print("   Checking data coverage...")
    chemicals_with_data = 0
    chemicals_without_data = []

    for chemical_name, hs_code in chemicals:
        has_data = False
        for period in ['3_months', '6_months', '9_months', '12_months']:
            if hs_code in transaction_data[period]:
                has_data = True
                break

        if has_data:
            chemicals_with_data += 1
        else:
            chemicals_without_data.append((chemical_name, hs_code))

    print(f"     Chemicals with data: {chemicals_with_data}/{len(chemicals)}")
    if chemicals_without_data:
        print(f"     Chemicals without data: {len(chemicals_without_data)}")
        for name, hs in chemicals_without_data[:5]:  # Show first 5
            print(f"       - {name} ({hs})")
        if len(chemicals_without_data) > 5:
            print(f"       ... and {len(chemicals_without_data) - 5} more")

def create_and_verify_final_csv(transaction_data, chemicals):
    """Create final CSV and verify its correctness"""

    headers = [
        'name_of_chemical',
        'hs_code',
        'countries_last_3_months',
        'exporters_last_3_months',
        'transactions_last_3_months',
        'countries_last_6_months',
        'exporters_last_6_months',
        'transactions_last_6_months',
        'countries_last_9_months',
        'exporters_last_9_months',
        'transactions_last_9_months',
        'countries_last_12_months',
        'exporters_last_12_months',
        'transactions_last_12_months'
    ]

    with open('chemical_transaction_data_verified.csv', 'w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        writer.writerow(headers)

        for chemical_name, hs_code in chemicals:
            row = [chemical_name, hs_code]

            # Add data for each time period
            for period in ['3_months', '6_months', '9_months', '12_months']:
                if hs_code in transaction_data[period]:
                    data = transaction_data[period][hs_code]
                    row.extend([
                        data['countries'],
                        data['exporters'],
                        data['transactions']
                    ])
                else:
                    row.extend([0, 0, 0])

            writer.writerow(row)

    # Verify the created file
    with open('chemical_transaction_data_verified.csv', 'r') as file:
        reader = csv.reader(file)
        rows = list(reader)

    print(f"   ✅ Created CSV with {len(rows)-1} data rows")
    print(f"   ✅ Headers: {len(rows[0])} columns")

    # Spot check a few rows
    print("   Sample data verification:")
    for i in [1, 2, len(rows)//2]:  # Check first, second, and middle row
        if i < len(rows):
            row = rows[i]
            print(f"     Row {i}: {row[0]} ({row[1]}) - 3m: {row[4]}, 6m: {row[7]}, 9m: {row[10]}, 12m: {row[13]} transactions")

if __name__ == "__main__":
    comprehensive_test()
