import csv
import re
from collections import defaultdict

def normalize_hs_code(hs_code):
    """Remove dots and standardize HS code format"""
    if isinstance(hs_code, str):
        return hs_code.replace('.', '').strip()
    return str(hs_code).replace('.', '').strip()

def parse_data_txt():
    """Parse the data.txt file to extract transaction data for all time periods"""
    transaction_data = {
        '3_months': {},
        '6_months': {},
        '9_months': {},
        '12_months': {}
    }
    
    current_period = None
    current_record = {}
    
    with open('data.txt', 'r') as file:
        for line in file:
            line = line.strip()
            
            # Identify time period
            if 'for 3 months' in line:
                current_period = '3_months'
                continue
            elif 'for 6 months' in line:
                current_period = '6_months'
                continue
            elif 'for 9months' in line:
                current_period = '9_months'
                continue
            elif 'for 12 months' in line:
                current_period = '12_months'
                continue
            
            # Parse record data
            if current_period and '|' in line:
                parts = line.split('|')
                if len(parts) == 2:
                    key = parts[0].strip()
                    value = parts[1].strip()
                    
                    if key == 'hs_code':
                        if current_record:  # Save previous record
                            hs_code = normalize_hs_code(current_record['hs_code'])
                            transaction_data[current_period][hs_code] = {
                                'countries': current_record.get('total_exporting_countries', 0),
                                'exporters': current_record.get('total_unique_exporters', 0),
                                'transactions': current_record.get('total_transactions', 0)
                            }
                        current_record = {'hs_code': value}
                    else:
                        current_record[key] = int(value) if value.isdigit() else value
    
    # Save the last record
    if current_record and current_period:
        hs_code = normalize_hs_code(current_record['hs_code'])
        transaction_data[current_period][hs_code] = {
            'countries': current_record.get('total_exporting_countries', 0),
            'exporters': current_record.get('total_unique_exporters', 0),
            'transactions': current_record.get('total_transactions', 0)
        }
    
    return transaction_data

def parse_chemical_csv():
    """Parse the chemical names and HS codes from CSV file"""
    chemicals = []
    
    with open('Supplier Retrieval Data validation - Sheet8.csv', 'r') as file:
        reader = csv.reader(file)
        next(reader)  # Skip header
        next(reader)  # Skip empty row
        
        for row in reader:
            if len(row) >= 2 and row[0].strip() and row[1].strip():
                chemical_name = row[0].strip()
                hs_code = normalize_hs_code(row[1].strip())
                chemicals.append((chemical_name, hs_code))
    
    return chemicals

def create_final_csv():
    """Create the final CSV with all chemical data"""
    print("Parsing transaction data...")
    transaction_data = parse_data_txt()
    
    print("Parsing chemical data...")
    chemicals = parse_chemical_csv()
    
    print("Creating final CSV...")
    
    # Define CSV headers
    headers = [
        'name_of_chemical',
        'hs_code',
        'countries_last_3_months',
        'exporters_last_3_months', 
        'transactions_last_3_months',
        'countries_last_6_months',
        'exporters_last_6_months',
        'transactions_last_6_months',
        'countries_last_9_months',
        'exporters_last_9_months',
        'transactions_last_9_months',
        'countries_last_12_months',
        'exporters_last_12_months',
        'transactions_last_12_months'
    ]
    
    with open('chemical_transaction_data.csv', 'w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        writer.writerow(headers)
        
        for chemical_name, hs_code in chemicals:
            row = [chemical_name, hs_code]
            
            # Add data for each time period
            for period in ['3_months', '6_months', '9_months', '12_months']:
                if hs_code in transaction_data[period]:
                    data = transaction_data[period][hs_code]
                    row.extend([
                        data['countries'],
                        data['exporters'],
                        data['transactions']
                    ])
                else:
                    # No data found for this HS code in this period
                    row.extend([0, 0, 0])
            
            writer.writerow(row)
    
    print(f"Successfully created chemical_transaction_data.csv with {len(chemicals)} chemicals")
    
    # Print summary
    print("\nSummary:")
    print(f"Total chemicals processed: {len(chemicals)}")
    
    # Count unique HS codes
    unique_hs_codes = set(hs_code for _, hs_code in chemicals)
    print(f"Unique HS codes: {len(unique_hs_codes)}")
    
    # Show chemicals with same HS codes
    hs_code_count = defaultdict(list)
    for chemical_name, hs_code in chemicals:
        hs_code_count[hs_code].append(chemical_name)
    
    shared_hs_codes = {hs: names for hs, names in hs_code_count.items() if len(names) > 1}
    if shared_hs_codes:
        print(f"\nChemicals sharing HS codes:")
        for hs_code, names in shared_hs_codes.items():
            print(f"  HS Code {hs_code}: {', '.join(names)}")

if __name__ == "__main__":
    create_final_csv()
