import csv
import re
from collections import defaultdict

def normalize_hs_code(hs_code):
    """Remove dots and standardize HS code format"""
    if isinstance(hs_code, str):
        return hs_code.replace('.', '').strip()
    return str(hs_code).replace('.', '').strip()

def parse_data_txt():
    """Parse the data.txt file to extract transaction data for all time periods"""
    transaction_data = {
        '3_months': {},
        '6_months': {},
        '9_months': {},
        '12_months': {}
    }
    
    current_period = None
    current_record = {}
    
    with open('data.txt', 'r') as file:
        for line_num, line in enumerate(file, 1):
            line = line.strip()
            
            # Identify time period
            if 'for 3 months' in line:
                current_period = '3_months'
                print(f"Line {line_num}: Found 3 months section")
                continue
            elif 'for 6 months' in line:
                current_period = '6_months'
                print(f"Line {line_num}: Found 6 months section")
                continue
            elif 'for 9months' in line:
                current_period = '9_months'
                print(f"Line {line_num}: Found 9 months section")
                continue
            elif 'for 12 months' in line:
                current_period = '12_months'
                print(f"Line {line_num}: Found 12 months section")
                continue
            
            # Parse record data
            if current_period and '|' in line:
                parts = line.split('|')
                if len(parts) == 2:
                    key = parts[0].strip()
                    value = parts[1].strip()
                    
                    if key == 'hs_code':
                        if current_record:  # Save previous record
                            hs_code = normalize_hs_code(current_record['hs_code'])
                            transaction_data[current_period][hs_code] = {
                                'countries': current_record.get('total_exporting_countries', 0),
                                'exporters': current_record.get('total_unique_exporters', 0),
                                'transactions': current_record.get('total_transactions', 0)
                            }
                            # Debug specific HS code
                            if hs_code == '34021300':
                                print(f"Line {line_num}: Saved {current_period} data for 34021300: {transaction_data[current_period][hs_code]}")
                        current_record = {'hs_code': value}
                        if normalize_hs_code(value) == '34021300':
                            print(f"Line {line_num}: Found HS code 34021300 in {current_period}")
                    else:
                        current_record[key] = int(value) if value.isdigit() else value
    
    # Save the last record
    if current_record and current_period:
        hs_code = normalize_hs_code(current_record['hs_code'])
        transaction_data[current_period][hs_code] = {
            'countries': current_record.get('total_exporting_countries', 0),
            'exporters': current_record.get('total_unique_exporters', 0),
            'transactions': current_record.get('total_transactions', 0)
        }
        if hs_code == '34021300':
            print(f"Final: Saved {current_period} data for 34021300: {transaction_data[current_period][hs_code]}")
    
    # Debug: Print all data for 34021300
    print("\nFinal data for HS code 34021300:")
    for period in ['3_months', '6_months', '9_months', '12_months']:
        if '34021300' in transaction_data[period]:
            print(f"  {period}: {transaction_data[period]['34021300']}")
        else:
            print(f"  {period}: NOT FOUND")
    
    return transaction_data

if __name__ == "__main__":
    parse_data_txt()
